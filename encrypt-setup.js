// 自动化加密设置脚本
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class EncryptionSetup {
    constructor() {
        this.algorithm = 'aes-256-gcm';
        this.keyLength = 32; // 256 bits
    }

    // 生成随机密钥
    generateKey() {
        return crypto.randomBytes(this.keyLength);
    }

    // 加密文本
    encrypt(text, key) {
        const iv = crypto.randomBytes(12); // 96 bits for GCM
        const cipher = crypto.createCipher(this.algorithm, key);
        cipher.setAAD(Buffer.from('x-enhancer-auth'));
        
        let encrypted = cipher.update(text, 'utf8', 'hex');
        encrypted += cipher.final('hex');
        
        const authTag = cipher.getAuthTag();
        
        return {
            iv: iv.toString('hex'),
            encrypted: encrypted,
            authTag: authTag.toString('hex')
        };
    }

    // 解密文本
    decrypt(encryptedData, key) {
        const decipher = crypto.createDecipher(this.algorithm, key);
        decipher.setAAD(<PERSON>uff<PERSON>.from('x-enhancer-auth'));
        decipher.setAuthTag(Buffer.from(encryptedData.authTag, 'hex'));
        
        let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
    }

    // 生成密钥文件
    generateKeyFile() {
        const key = this.generateKey();
        const keyFile = {
            version: '1.0',
            algorithm: this.algorithm,
            keyLength: this.keyLength * 8, // 转换为位数
            key: key.toString('hex'),
            timestamp: Date.now(),
            checksum: this.calculateChecksum(key.toString('hex'))
        };

        return {
            keyFile: keyFile,
            keyBuffer: key
        };
    }

    // 计算校验和
    calculateChecksum(data) {
        return crypto.createHash('sha256').update(data).digest('hex').substring(0, 16);
    }

    // 验证密钥文件
    validateKeyFile(keyFileContent) {
        try {
            const keyFile = JSON.parse(keyFileContent);
            
            if (!keyFile.version || !keyFile.algorithm || !keyFile.key || !keyFile.checksum) {
                throw new Error('密钥文件格式无效');
            }

            if (keyFile.algorithm !== this.algorithm) {
                throw new Error('不支持的加密算法');
            }

            const calculatedChecksum = this.calculateChecksum(keyFile.key);
            if (calculatedChecksum !== keyFile.checksum) {
                throw new Error('密钥文件已损坏或被篡改');
            }

            return keyFile;
        } catch (error) {
            throw new Error(`密钥文件验证失败: ${error.message}`);
        }
    }

    // 加密文件
    encryptFile(filePath, key) {
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const encrypted = this.encrypt(fileContent, key);
        
        const encryptedFile = {
            version: '1.0',
            originalName: path.basename(filePath),
            encryptedData: encrypted,
            timestamp: Date.now()
        };

        return encryptedFile;
    }

    // 执行完整的加密设置
    async setupEncryption() {
        console.log('🔐 开始设置加密系统...');

        try {
            // 1. 生成密钥文件
            console.log('📝 生成密钥文件...');
            const { keyFile, keyBuffer } = this.generateKeyFile();
            
            // 保存密钥文件
            const keyFilePath = path.join(__dirname, 'x-enhancer-key.json');
            fs.writeFileSync(keyFilePath, JSON.stringify(keyFile, null, 2));
            console.log(`✅ 密钥文件已保存: ${keyFilePath}`);

            // 2. 加密content.js文件
            console.log('🔒 加密核心代码文件...');
            const contentJsPath = path.join(__dirname, 'content.js');
            
            if (!fs.existsSync(contentJsPath)) {
                throw new Error('找不到content.js文件');
            }

            const encryptedContent = this.encryptFile(contentJsPath, keyBuffer);
            
            // 保存加密文件
            const encryptedFilePath = path.join(__dirname, 'content.encrypted.json');
            fs.writeFileSync(encryptedFilePath, JSON.stringify(encryptedContent, null, 2));
            console.log(`✅ 加密文件已保存: ${encryptedFilePath}`);

            // 3. 备份原始文件
            const backupPath = path.join(__dirname, 'content.js.backup');
            fs.copyFileSync(contentJsPath, backupPath);
            console.log(`✅ 原始文件已备份: ${backupPath}`);

            console.log('\n🎉 加密设置完成！');
            console.log('\n📋 下一步操作：');
            console.log('1. 妥善保管生成的密钥文件: x-enhancer-key.json');
            console.log('2. 原始content.js文件已备份为: content.js.backup');
            console.log('3. 加密后的文件: content.encrypted.json');
            console.log('4. 现在可以删除原始的content.js文件（已备份）');

            return {
                keyFilePath,
                encryptedFilePath,
                backupPath
            };

        } catch (error) {
            console.error('❌ 加密设置失败:', error.message);
            throw error;
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const setup = new EncryptionSetup();
    setup.setupEncryption().catch(console.error);
}

module.exports = EncryptionSetup;
