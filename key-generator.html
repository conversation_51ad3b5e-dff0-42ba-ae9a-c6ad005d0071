<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>X User Info Enhancer - 密钥生成器</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #1da1f2;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #e1e8ed;
            border-radius: 8px;
        }
        
        .section h3 {
            margin-top: 0;
            color: #14171a;
        }
        
        button {
            background-color: #1da1f2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: background-color 0.2s;
        }
        
        button:hover {
            background-color: #1991db;
        }
        
        button:disabled {
            background-color: #aab8c2;
            cursor: not-allowed;
        }
        
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .file-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
        }
        
        .step {
            margin-bottom: 15px;
            padding-left: 20px;
            position: relative;
        }
        
        .step::before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 0;
            background-color: #1da1f2;
            color: white;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .steps {
            counter-reset: step-counter;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 X User Info Enhancer 密钥生成器</h1>
        
        <div class="warning">
            <strong>⚠️ 重要提示：</strong>
            <ul>
                <li>生成的密钥文件是插件正常运行的必要条件</li>
                <li>请妥善保管密钥文件，丢失后将无法使用插件</li>
                <li>不要将密钥文件分享给他人</li>
            </ul>
        </div>

        <div class="section">
            <h3>📋 使用说明</h3>
            <div class="steps">
                <div class="step">点击下方"生成密钥文件"按钮</div>
                <div class="step">下载生成的密钥文件到安全位置</div>
                <div class="step">在插件中加载该密钥文件以启用功能</div>
            </div>
        </div>

        <div class="section">
            <h3>🔑 密钥生成</h3>
            <button id="generateBtn" onclick="generateKey()">生成密钥文件</button>
            <div id="generateStatus"></div>
            <div id="keyInfo" class="file-info" style="display: none;"></div>
        </div>

        <div class="section">
            <h3>🔒 文件加密</h3>
            <p>使用生成的密钥加密核心代码文件：</p>
            <input type="file" id="fileInput" accept=".js" style="margin-bottom: 10px;">
            <br>
            <button id="encryptBtn" onclick="encryptFile()" disabled>加密文件</button>
            <div id="encryptStatus"></div>
        </div>
    </div>

    <script src="crypto-utils.js"></script>
    <script>
        let currentKey = null;
        const cryptoUtils = new CryptoUtils();

        async function generateKey() {
            const generateBtn = document.getElementById('generateBtn');
            const generateStatus = document.getElementById('generateStatus');
            const keyInfo = document.getElementById('keyInfo');
            const encryptBtn = document.getElementById('encryptBtn');

            try {
                generateBtn.disabled = true;
                generateStatus.innerHTML = '<div class="status">正在生成密钥...</div>';

                const keyFileContent = await cryptoUtils.generateKeyFile();
                const keyFile = JSON.parse(keyFileContent);
                currentKey = await cryptoUtils.importKey(keyFile.key);

                // 显示密钥信息
                keyInfo.innerHTML = `
                    <strong>密钥文件信息：</strong><br>
                    版本: ${keyFile.version}<br>
                    算法: ${keyFile.algorithm}<br>
                    密钥长度: ${keyFile.keyLength} 位<br>
                    生成时间: ${new Date(keyFile.timestamp).toLocaleString()}<br>
                    校验和: ${keyFile.checksum}
                `;
                keyInfo.style.display = 'block';

                // 下载密钥文件
                const blob = new Blob([keyFileContent], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'x-enhancer-key.json';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                generateStatus.innerHTML = '<div class="status success">✅ 密钥文件生成成功并已下载！</div>';
                encryptBtn.disabled = false;

            } catch (error) {
                generateStatus.innerHTML = `<div class="status error">❌ 生成失败: ${error.message}</div>`;
            } finally {
                generateBtn.disabled = false;
            }
        }

        async function encryptFile() {
            const fileInput = document.getElementById('fileInput');
            const encryptBtn = document.getElementById('encryptBtn');
            const encryptStatus = document.getElementById('encryptStatus');

            if (!fileInput.files[0]) {
                encryptStatus.innerHTML = '<div class="status error">❌ 请选择要加密的文件</div>';
                return;
            }

            if (!currentKey) {
                encryptStatus.innerHTML = '<div class="status error">❌ 请先生成密钥</div>';
                return;
            }

            try {
                encryptBtn.disabled = true;
                encryptStatus.innerHTML = '<div class="status">正在加密文件...</div>';

                const file = fileInput.files[0];
                const fileContent = await file.text();
                
                const encryptedData = await cryptoUtils.encrypt(fileContent, currentKey);
                
                // 创建加密文件内容
                const encryptedFile = {
                    version: '1.0',
                    originalName: file.name,
                    encryptedData: encryptedData,
                    timestamp: Date.now()
                };

                // 下载加密文件
                const blob = new Blob([JSON.stringify(encryptedFile, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = file.name.replace('.js', '.encrypted.json');
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                encryptStatus.innerHTML = '<div class="status success">✅ 文件加密成功并已下载！</div>';

            } catch (error) {
                encryptStatus.innerHTML = `<div class="status error">❌ 加密失败: ${error.message}</div>`;
            } finally {
                encryptBtn.disabled = false;
            }
        }
    </script>
</body>
</html>
