document.addEventListener('DOMContentLoaded', () => {
  const enabledToggle = document.getElementById('enabledToggle');
  const keyFileInput = document.getElementById('keyFileInput');
  const loadKeyBtn = document.getElementById('loadKeyBtn');
  const clearKeyBtn = document.getElementById('clearKeyBtn');
  const authSection = document.getElementById('authSection');
  const authStatus = document.getElementById('authStatus');
  const statusText = document.getElementById('statusText');
  const keyInputContainer = document.getElementById('keyInputContainer');
  const keyInfo = document.getElementById('keyInfo');
  const message = document.getElementById('message');

  // 文本常量
  const TEXT = {
    NEED_AUTH: '需要密钥文件授权',
    AUTHORIZED: '已授权',
    KEY_INFO: '密钥信息',
    VERSION: '版本',
    ALGORITHM: '算法',
    GENERATE_TIME: '生成时间',
    SELECT_FILE: '请选择密钥文件',
    LOAD_SUCCESS: '密钥文件加载成功！',
    KEY_CLEARED: '密钥文件已清除',
    CONFIRM_CLEAR: '确定要清除密钥文件吗？清除后插件将无法正常工作。',
    VALIDATION_FAILED: '密钥验证失败，请重新加载密钥文件',
    INVALID_FORMAT: '密钥文件格式无效',
    FILE_CORRUPTED: '密钥文件已损坏或被篡改',
    VALIDATION_ERROR: '密钥文件验证失败'
  };

  // 加密保护类
  class PopupEncryption {
    calculateChecksum(data) {
      let hash = 0;
      for (let i = 0; i < data.length; i++) {
        const char = data.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }
      return hash.toString(16);
    }

    validateKeyFile(keyFileContent) {
      try {
        const keyFile = JSON.parse(keyFileContent);
        
        if (!keyFile.version || !keyFile.algorithm || !keyFile.key || !keyFile.checksum) {
          throw new Error(TEXT.INVALID_FORMAT);
        }

        const calculatedChecksum = this.calculateChecksum(keyFile.key);
        if (calculatedChecksum !== keyFile.checksum) {
          throw new Error(TEXT.FILE_CORRUPTED);
        }

        return keyFile;
      } catch (error) {
        throw new Error(`${TEXT.VALIDATION_ERROR}: ${error.message}`);
      }
    }
  }

  const encryption = new PopupEncryption();

  // 显示消息
  function showMessage(text, type = 'error') {
    message.textContent = text;
    message.className = `message ${type}`;
    message.style.display = 'block';
    
    setTimeout(() => {
      message.style.display = 'none';
    }, 5000);
  }

  // 更新授权状态UI
  function updateAuthUI(isUnlocked, keyFile = null) {
    if (isUnlocked) {
      authSection.classList.add('unlocked');
      authStatus.className = 'status unlocked';
      statusText.textContent = TEXT.AUTHORIZED;
      keyInputContainer.style.display = 'none';
      clearKeyBtn.style.display = 'block';
      
      if (keyFile) {
        keyInfo.innerHTML = `
          <strong>${TEXT.KEY_INFO}:</strong><br>
          ${TEXT.VERSION}: ${keyFile.version}<br>
          ${TEXT.ALGORITHM}: ${keyFile.algorithm}<br>
          ${TEXT.GENERATE_TIME}: ${new Date(keyFile.timestamp).toLocaleString()}
        `;
        keyInfo.style.display = 'block';
      }
    } else {
      authSection.classList.remove('unlocked');
      authStatus.className = 'status locked';
      statusText.textContent = TEXT.NEED_AUTH;
      keyInputContainer.style.display = 'block';
      clearKeyBtn.style.display = 'none';
      keyInfo.style.display = 'none';
    }
  }

  // 检查当前授权状态
  function checkAuthStatus() {
    chrome.storage.local.get(['keyFileContent', 'isUnlocked'], (data) => {
      if (data.keyFileContent && data.isUnlocked) {
        try {
          const keyFile = encryption.validateKeyFile(data.keyFileContent);
          updateAuthUI(true, keyFile);
        } catch (error) {
          updateAuthUI(false);
          showMessage(TEXT.VALIDATION_FAILED, 'error');
        }
      } else {
        updateAuthUI(false);
      }
    });
  }

  // 加载密钥文件
  loadKeyBtn.addEventListener('click', () => {
    const file = keyFileInput.files[0];
    if (!file) {
      showMessage(TEXT.SELECT_FILE, 'error');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const keyFileContent = e.target.result;
        const keyFile = encryption.validateKeyFile(keyFileContent);
        
        // 保存密钥文件内容
        chrome.storage.local.set({
          keyFileContent: keyFileContent,
          isUnlocked: true
        }, () => {
          updateAuthUI(true, keyFile);
          showMessage(TEXT.LOAD_SUCCESS, 'success');
          
          // 通知content script重新加载
          chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
            if (tabs[0] && (tabs[0].url.includes('x.com') || tabs[0].url.includes('twitter.com'))) {
              chrome.tabs.reload(tabs[0].id);
            }
          });
        });
        
      } catch (error) {
        showMessage(error.message, 'error');
      }
    };
    
    reader.readAsText(file);
  });

  // 清除密钥
  clearKeyBtn.addEventListener('click', () => {
    if (confirm(TEXT.CONFIRM_CLEAR)) {
      chrome.storage.local.remove(['keyFileContent', 'isUnlocked'], () => {
        updateAuthUI(false);
        showMessage(TEXT.KEY_CLEARED, 'success');
        
        // 通知content script重新加载
        chrome.tabs.query({active: true, currentWindow: true}, (tabs) => {
          if (tabs[0] && (tabs[0].url.includes('x.com') || tabs[0].url.includes('twitter.com'))) {
            chrome.tabs.reload(tabs[0].id);
          }
        });
      });
    }
  });

  // Load saved state
  chrome.storage.local.get('isEnabled', (data) => {
    enabledToggle.checked = data.isEnabled === undefined ? true : !!data.isEnabled;
  });

  // Save state on change
  enabledToggle.addEventListener('change', () => {
    chrome.storage.local.set({ isEnabled: enabledToggle.checked });
  });

  // 初始化检查授权状态
  checkAuthStatus();
});
