# X User Info Enhancer - 加密版本使用说明

## 🔐 加密保护机制

本插件已实现加密保护机制，核心代码经过加密处理，需要密钥文件才能正常运行。

## 📁 文件说明

### 核心文件
- `content.js` - 加密保护的启动器，负责验证密钥和解密核心代码
- `content.encrypted.json` - 加密后的核心代码文件
- `x-enhancer-key.json` - 密钥文件（必需）
- `content.js.backup` - 原始代码备份

### 工具文件
- `key-generator.html` - 密钥生成器（可选，用于重新生成密钥）
- `crypto-utils.js` - 加密工具库
- `setup-encryption.js` - 加密设置工具

## 🚀 使用步骤

### 1. 安装插件
1. 在Chrome浏览器中打开 `chrome://extensions/`
2. 开启"开发者模式"
3. 点击"加载已解压的扩展程序"
4. 选择插件文件夹

### 2. 加载密钥文件
1. 点击浏览器工具栏中的插件图标
2. 在弹出的界面中点击"选择文件"
3. 选择 `x-enhancer-key.json` 密钥文件
4. 点击"加载密钥文件"
5. 看到"已授权"状态后，插件即可正常使用

### 3. 使用插件
- 访问 X.com 或 Twitter.com
- 插件会自动在推文中显示用户的详细信息
- 包括注册时间、帖子数、关注数等

## ⚠️ 重要提示

### 密钥文件安全
- **妥善保管密钥文件**：丢失后插件将无法使用
- **不要分享密钥文件**：这是插件运行的唯一凭证
- **备份密钥文件**：建议保存多个副本

### 故障排除
1. **插件无法启动**
   - 检查是否已加载密钥文件
   - 确认密钥文件格式正确
   - 尝试重新加载密钥文件

2. **密钥验证失败**
   - 确认使用的是正确的密钥文件
   - 检查文件是否损坏
   - 联系开发者获取新的密钥文件

3. **功能异常**
   - 刷新页面重试
   - 检查插件是否已启用
   - 查看浏览器控制台错误信息

## 🔧 技术细节

### 加密机制
- 使用Base64编码存储核心代码
- 密钥文件包含校验和验证
- 运行时动态解密和执行代码

### 安全特性
- 核心代码不以明文形式存在
- 密钥验证防止篡改
- 无密钥无法查看或运行核心功能

### 文件结构
```
XUserInfoGet/
├── manifest.json          # 插件配置
├── content.js             # 加密保护启动器
├── content.encrypted.json # 加密的核心代码
├── x-enhancer-key.json    # 密钥文件
├── popup.html             # 插件界面
├── popup.js               # 界面逻辑
├── interceptor.js         # 数据拦截器
├── styles.css             # 样式文件
└── icons/                 # 图标文件
```

## 📞 支持

如果遇到问题或需要技术支持，请：
1. 检查本文档的故障排除部分
2. 查看浏览器控制台的错误信息
3. 联系开发者获取帮助

## 🔄 更新说明

- v1.0 - 初始加密版本
- 实现了基础的密钥验证和代码保护机制
- 支持密钥文件管理界面

---

**注意**：此加密机制主要用于代码保护和访问控制，在实际生产环境中建议使用更强的加密算法。
