# 🔐 X User Info Enhancer 加密版 - 修复版安装指南

## 🛠️ 乱码问题已修复

我们已经修复了插件界面的中文乱码问题，现在使用简化版本的界面文件。

## 📁 更新后的文件结构

### 核心文件
- `manifest.json` - 插件配置（已更新）
- `content.js` - 加密保护启动器
- `content.encrypted.json` - 加密的核心代码
- `x-enhancer-key.json` - 密钥文件 ⚠️ **必需**
- `popup-simple.html` - 简化版界面（无乱码）
- `popup-simple.js` - 简化版界面逻辑
- `interceptor.js` - 数据拦截器
- `styles.css` - 样式文件

### 测试文件
- `test-popup.html` - 界面测试工具
- `test-encryption.html` - 加密系统测试

## 🚀 安装步骤

### 1. 准备文件
确保您有以下文件：
```
XUserInfoGet/
├── manifest.json
├── popup-simple.html      ← 新的界面文件
├── popup-simple.js        ← 新的界面逻辑
├── content.js
├── content.encrypted.json
├── x-enhancer-key.json    ← 密钥文件
├── interceptor.js
├── styles.css
└── icons/
    ├── icon16.png
    ├── icon48.png
    └── icon128.png
```

### 2. 安装到Chrome
1. 打开Chrome浏览器
2. 地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹

### 3. 验证安装
1. 插件应该出现在扩展程序列表中
2. 点击插件图标，界面应该正常显示中文
3. 不应该出现乱码或特殊字符

### 4. 加载密钥文件
1. 点击浏览器工具栏中的插件图标
2. 界面应显示"需要密钥文件授权"
3. 点击"选择文件"，选择 `x-enhancer-key.json`
4. 点击"加载密钥文件"
5. 状态应变为"已授权"

### 5. 测试功能
1. 访问 [X.com](https://x.com) 或 [Twitter.com](https://twitter.com)
2. 插件应自动在推文中显示用户信息
3. 包括：注册时间、帖子数、关注数、粉丝数

## 🔧 故障排除

### 界面问题
| 问题 | 解决方案 |
|------|----------|
| 界面显示乱码 | 使用 `popup-simple.html` 版本 |
| 按钮文字异常 | 检查浏览器编码设置 |
| 界面布局错乱 | 清除浏览器缓存重试 |

### 功能问题
| 问题 | 解决方案 |
|------|----------|
| 插件无法启动 | 检查密钥文件是否正确加载 |
| 显示"需要授权" | 重新选择并加载密钥文件 |
| 功能不工作 | 刷新页面，确保插件已启用 |

## 🧪 测试工具

### 界面测试
打开 `test-popup.html` 查看界面预览和文件检查

### 加密系统测试
打开 `test-encryption.html` 验证加密系统

## 📞 技术支持

### 常见问题
1. **Q: 为什么要使用简化版界面？**
   A: 简化版避免了字符编码问题，确保在所有环境下都能正常显示中文。

2. **Q: 原版界面还能用吗？**
   A: 可以，但可能在某些环境下出现乱码。建议使用简化版。

3. **Q: 如何切换回原版界面？**
   A: 修改 `manifest.json` 中的 `default_popup` 为 `popup.html`。

### 联系方式
遇到问题请：
1. 检查本指南的故障排除部分
2. 使用测试工具验证系统状态
3. 查看浏览器控制台错误信息

---

**版本**: v1.1 修复版  
**更新内容**: 修复中文乱码问题  
**兼容性**: Chrome 88+

## ✅ 验证清单

安装完成后请验证：
- [ ] 插件图标正常显示
- [ ] 点击图标弹出界面无乱码
- [ ] 能够正常加载密钥文件
- [ ] 状态显示"已授权"
- [ ] 在X.com上能看到用户信息增强
