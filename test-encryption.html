<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>加密系统测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1, h2 {
            color: #1da1f2;
        }
        
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        button {
            background-color: #1da1f2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background-color: #1991db;
        }
        
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 X User Info Enhancer 加密系统测试</h1>
        
        <div class="container">
            <h2>1. 密钥文件验证</h2>
            <button onclick="testKeyFile()">测试密钥文件</button>
            <div id="keyTestResult"></div>
        </div>

        <div class="container">
            <h2>2. 加密文件验证</h2>
            <button onclick="testEncryptedFile()">测试加密文件</button>
            <div id="encryptedTestResult"></div>
        </div>

        <div class="container">
            <h2>3. 解密功能测试</h2>
            <button onclick="testDecryption()">测试解密功能</button>
            <div id="decryptionTestResult"></div>
        </div>

        <div class="container">
            <h2>4. 完整流程测试</h2>
            <button onclick="testFullFlow()">测试完整流程</button>
            <div id="fullFlowTestResult"></div>
        </div>
    </div>

    <script>
        // 加密保护类（简化版）
        class TestEncryption {
            calculateChecksum(data) {
                let hash = 0;
                for (let i = 0; i < data.length; i++) {
                    const char = data.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash;
                }
                return hash.toString(16);
            }

            validateKeyFile(keyFileContent) {
                try {
                    const keyFile = JSON.parse(keyFileContent);
                    
                    if (!keyFile.version || !keyFile.algorithm || !keyFile.key || !keyFile.checksum) {
                        throw new Error('密钥文件格式无效');
                    }

                    const calculatedChecksum = this.calculateChecksum(keyFile.key);
                    if (calculatedChecksum !== keyFile.checksum) {
                        throw new Error('密钥文件已损坏或被篡改');
                    }

                    return keyFile;
                } catch (error) {
                    throw new Error(`密钥文件验证失败: ${error.message}`);
                }
            }

            decrypt(encryptedText, key) {
                try {
                    return atob(encryptedText);
                } catch (error) {
                    throw new Error('解密失败');
                }
            }
        }

        const encryption = new TestEncryption();

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${type}">${message}</div>`;
        }

        async function testKeyFile() {
            try {
                const response = await fetch('x-enhancer-key.json');
                const keyFileContent = await response.text();
                
                const keyFile = encryption.validateKeyFile(keyFileContent);
                
                showResult('keyTestResult', `
                    ✅ 密钥文件验证成功！<br>
                    版本: ${keyFile.version}<br>
                    算法: ${keyFile.algorithm}<br>
                    密钥: ${keyFile.key}<br>
                    校验和: ${keyFile.checksum}<br>
                    生成时间: ${new Date(keyFile.timestamp).toLocaleString()}
                `, 'success');
                
            } catch (error) {
                showResult('keyTestResult', `❌ 密钥文件测试失败: ${error.message}`, 'error');
            }
        }

        async function testEncryptedFile() {
            try {
                const response = await fetch('content.encrypted.json');
                const encryptedFile = await response.json();
                
                if (!encryptedFile.version || !encryptedFile.originalName || !encryptedFile.encryptedData) {
                    throw new Error('加密文件格式无效');
                }
                
                showResult('encryptedTestResult', `
                    ✅ 加密文件验证成功！<br>
                    版本: ${encryptedFile.version}<br>
                    原始文件名: ${encryptedFile.originalName}<br>
                    加密数据长度: ${encryptedFile.encryptedData.length} 字符<br>
                    生成时间: ${new Date(encryptedFile.timestamp).toLocaleString()}
                `, 'success');
                
            } catch (error) {
                showResult('encryptedTestResult', `❌ 加密文件测试失败: ${error.message}`, 'error');
            }
        }

        async function testDecryption() {
            try {
                // 获取密钥文件
                const keyResponse = await fetch('x-enhancer-key.json');
                const keyFileContent = await keyResponse.text();
                const keyFile = encryption.validateKeyFile(keyFileContent);
                
                // 获取加密文件
                const encryptedResponse = await fetch('content.encrypted.json');
                const encryptedFile = await encryptedResponse.json();
                
                // 解密
                const decryptedCode = encryption.decrypt(encryptedFile.encryptedData, keyFile.key);
                
                showResult('decryptionTestResult', `
                    ✅ 解密测试成功！<br>
                    解密后代码长度: ${decryptedCode.length} 字符<br>
                    代码预览:<br>
                    <pre>${decryptedCode.substring(0, 200)}...</pre>
                `, 'success');
                
            } catch (error) {
                showResult('decryptionTestResult', `❌ 解密测试失败: ${error.message}`, 'error');
            }
        }

        async function testFullFlow() {
            try {
                showResult('fullFlowTestResult', '🔄 正在执行完整流程测试...', 'info');
                
                // 步骤1: 验证密钥文件
                const keyResponse = await fetch('x-enhancer-key.json');
                const keyFileContent = await keyResponse.text();
                const keyFile = encryption.validateKeyFile(keyFileContent);
                
                // 步骤2: 验证加密文件
                const encryptedResponse = await fetch('content.encrypted.json');
                const encryptedFile = await encryptedResponse.json();
                
                // 步骤3: 解密代码
                const decryptedCode = encryption.decrypt(encryptedFile.encryptedData, keyFile.key);
                
                // 步骤4: 验证解密后的代码
                if (!decryptedCode.includes('X User Info Enhancer') || !decryptedCode.includes('handleData')) {
                    throw new Error('解密后的代码内容验证失败');
                }
                
                showResult('fullFlowTestResult', `
                    ✅ 完整流程测试成功！<br>
                    ✓ 密钥文件验证通过<br>
                    ✓ 加密文件验证通过<br>
                    ✓ 解密功能正常<br>
                    ✓ 代码内容验证通过<br>
                    <br>
                    🎉 加密保护系统运行正常！
                `, 'success');
                
            } catch (error) {
                showResult('fullFlowTestResult', `❌ 完整流程测试失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                testFullFlow();
            }, 1000);
        });
    </script>
</body>
</html>
