<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试插件界面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1, h2 {
            color: #1da1f2;
        }
        
        .popup-preview {
            border: 2px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background-color: #f9f9f9;
            max-width: 350px;
            margin: 20px auto;
        }
        
        iframe {
            border: none;
            width: 100%;
            height: 400px;
        }
        
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .steps {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        
        .steps ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .steps li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 插件界面测试</h1>
        
        <div class="info">
            <strong>说明：</strong>这是插件界面的预览版本，用于测试中文字符显示是否正常。
        </div>

        <div class="container">
            <h2>插件界面预览</h2>
            <div class="popup-preview">
                <iframe src="popup-simple.html"></iframe>
            </div>
        </div>

        <div class="container">
            <h2>使用步骤</h2>
            <div class="steps">
                <strong>📋 安装和使用步骤：</strong>
                <ol>
                    <li>在Chrome中加载插件（开发者模式）</li>
                    <li>点击浏览器工具栏中的插件图标</li>
                    <li>选择 <code>x-enhancer-key.json</code> 密钥文件</li>
                    <li>点击"加载密钥文件"按钮</li>
                    <li>看到"已授权"状态后即可使用</li>
                    <li>访问 X.com 或 Twitter.com 测试功能</li>
                </ol>
            </div>
        </div>

        <div class="container">
            <h2>故障排除</h2>
            <div class="info">
                <strong>如果界面显示乱码：</strong>
                <ul>
                    <li>确保浏览器支持UTF-8编码</li>
                    <li>检查文件是否正确保存为UTF-8格式</li>
                    <li>尝试重新加载插件</li>
                    <li>使用简化版本的popup界面</li>
                </ul>
            </div>
        </div>

        <div class="container">
            <h2>文件检查</h2>
            <div id="fileCheck">
                <button onclick="checkFiles()">检查必要文件</button>
                <div id="checkResult"></div>
            </div>
        </div>
    </div>

    <script>
        async function checkFiles() {
            const resultDiv = document.getElementById('checkResult');
            resultDiv.innerHTML = '<p>正在检查文件...</p>';
            
            const requiredFiles = [
                'manifest.json',
                'popup-simple.html',
                'popup-simple.js',
                'content.js',
                'content.encrypted.json',
                'x-enhancer-key.json',
                'interceptor.js',
                'styles.css'
            ];
            
            let results = [];
            
            for (const file of requiredFiles) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        results.push(`✅ ${file} - 存在`);
                    } else {
                        results.push(`❌ ${file} - 不存在`);
                    }
                } catch (error) {
                    results.push(`❌ ${file} - 检查失败`);
                }
            }
            
            resultDiv.innerHTML = `
                <div class="info">
                    <strong>文件检查结果：</strong><br>
                    ${results.join('<br>')}
                </div>
            `;
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(checkFiles, 1000);
        });
    </script>
</body>
</html>
