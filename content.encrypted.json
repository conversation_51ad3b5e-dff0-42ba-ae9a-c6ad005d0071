{"version": "1.0", "originalName": "content.js", "encryptedData": "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", "timestamp": 1725502320000}