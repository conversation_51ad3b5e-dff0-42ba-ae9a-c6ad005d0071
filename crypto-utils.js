// 加密工具模块
class CryptoUtils {
    constructor() {
        this.algorithm = 'AES-GCM';
        this.keyLength = 256;
    }

    // 生成随机密钥
    async generateKey() {
        return await crypto.subtle.generateKey(
            {
                name: this.algorithm,
                length: this.keyLength,
            },
            true,
            ['encrypt', 'decrypt']
        );
    }

    // 将密钥导出为可存储的格式
    async exportKey(key) {
        const exported = await crypto.subtle.exportKey('raw', key);
        return Array.from(new Uint8Array(exported));
    }

    // 从存储格式导入密钥
    async importKey(keyData) {
        const keyBuffer = new Uint8Array(keyData);
        return await crypto.subtle.importKey(
            'raw',
            keyBuffer,
            {
                name: this.algorithm,
                length: this.keyLength,
            },
            true,
            ['encrypt', 'decrypt']
        );
    }

    // 加密文本
    async encrypt(text, key) {
        const encoder = new TextEncoder();
        const data = encoder.encode(text);
        
        // 生成随机IV
        const iv = crypto.getRandomValues(new Uint8Array(12));
        
        const encrypted = await crypto.subtle.encrypt(
            {
                name: this.algorithm,
                iv: iv,
            },
            key,
            data
        );

        // 将IV和加密数据组合
        const result = new Uint8Array(iv.length + encrypted.byteLength);
        result.set(iv);
        result.set(new Uint8Array(encrypted), iv.length);
        
        return Array.from(result);
    }

    // 解密文本
    async decrypt(encryptedData, key) {
        const data = new Uint8Array(encryptedData);
        
        // 提取IV和加密数据
        const iv = data.slice(0, 12);
        const encrypted = data.slice(12);

        const decrypted = await crypto.subtle.decrypt(
            {
                name: this.algorithm,
                iv: iv,
            },
            key,
            encrypted
        );

        const decoder = new TextDecoder();
        return decoder.decode(decrypted);
    }

    // 生成密钥文件内容
    async generateKeyFile() {
        const key = await this.generateKey();
        const exportedKey = await this.exportKey(key);
        
        const keyFile = {
            version: '1.0',
            algorithm: this.algorithm,
            keyLength: this.keyLength,
            key: exportedKey,
            timestamp: Date.now(),
            checksum: this.calculateChecksum(exportedKey)
        };

        return JSON.stringify(keyFile, null, 2);
    }

    // 验证密钥文件
    async validateKeyFile(keyFileContent) {
        try {
            const keyFile = JSON.parse(keyFileContent);
            
            // 验证必要字段
            if (!keyFile.version || !keyFile.algorithm || !keyFile.key || !keyFile.checksum) {
                throw new Error('密钥文件格式无效');
            }

            // 验证算法
            if (keyFile.algorithm !== this.algorithm) {
                throw new Error('不支持的加密算法');
            }

            // 验证校验和
            const calculatedChecksum = this.calculateChecksum(keyFile.key);
            if (calculatedChecksum !== keyFile.checksum) {
                throw new Error('密钥文件已损坏或被篡改');
            }

            return keyFile;
        } catch (error) {
            throw new Error(`密钥文件验证失败: ${error.message}`);
        }
    }

    // 计算校验和
    calculateChecksum(data) {
        const str = Array.isArray(data) ? data.join(',') : String(data);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString(16);
    }
}

// 导出工具类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CryptoUtils;
} else {
    window.CryptoUtils = CryptoUtils;
}
