// This script runs in the isolated content script world.

// 全局用户数据存储
const globalUserData = new Map();
let mutationObserver = null;
let isEnabled = false;

const init = () => {
    console.log('X User Info Enhancer: Content script loaded.');

    // 1. Check if the functionality is enabled
    chrome.storage.local.get('isEnabled', (data) => {
        isEnabled = data.isEnabled === undefined ? true : !!data.isEnabled;
        console.log('X User Info Enhancer: Is enabled?', isEnabled);
        if (isEnabled) {
            // 2. Inject the interceptor script into the page's main world
            const s = document.createElement('script');
            s.src = chrome.runtime.getURL('interceptor.js');
            (document.head || document.documentElement).appendChild(s);
            s.onload = () => s.remove(); // Clean up the script tag after it has run

            // 3. Listen for the data event from the interceptor
            window.addEventListener('XUserInfoData', (event) => {
                console.log('X User Info Enhancer: Received data from interceptor.');
                handleData(event.detail);
            });

            // 4. 启动DOM监听器
            startDOMObserver();

            // 5. 定期重新注入（处理虚拟滚动）
            startPeriodicInjection();
        }
    });

    // Reload page if user toggles the switch
    chrome.storage.onChanged.addListener((changes) => {
        if (changes.isEnabled) {
            window.location.reload();
        }
    });
};

const handleData = (data) => {
    const users = new Map();

    // Recursive function to find all user objects
    const findUsers = (obj) => {
        if (!obj || typeof obj !== 'object') return;

        // Check if the current object is a user result
        // A user object has __typename: 'User' and typically a legacy property
        if (obj.__typename === 'User' && (obj.legacy || obj.core)) {
            const userLegacy = obj.legacy;
            const userCore = obj.core;

            // Determine screen_name, which is crucial for the map key and DOM lookup
            const screenName = userLegacy?.screen_name || userCore?.screen_name;

            if (screenName && !users.has(screenName)) {
                // Combine data from legacy and core, prioritizing legacy where applicable
                const extractedData = {
                    "创建时间": userLegacy?.created_at || userCore?.created_at,
                    "昵称": userLegacy?.name || userCore?.name,
                    "用户名": screenName,
                    "关注者": userLegacy?.followers_count ?? userLegacy?.normal_followers_count,
                    "正在关注": userLegacy?.friends_count,
                    "帖子数": userLegacy?.statuses_count
                };
                users.set(screenName, extractedData);

                // 将新数据添加到全局存储
                globalUserData.set(screenName, extractedData);
            }
        }

        // Recurse into arrays and objects
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                findUsers(obj[key]);
            }
        }
    };

    findUsers(data);

    if (users.size > 0) {
        console.log("--- ✔️ 提取成功! ✔️ ---");
        console.log("已提取的用户信息:", users);
        // Use a timeout to ensure the DOM has time to render after the API call
        setTimeout(() => injectUserInfo(users), 500);
    } else {
        console.log("--- ❌ 提取失败! ❌ ---");
        console.log("未找到任何用户信息。请检查数据结构或页面内容。");
    }
};

const injectUserInfo = (users) => {
  console.log('X User Info Enhancer: Starting DOM injection.');
  users.forEach((userData, screenName) => {
    // 查找包含该用户链接的推文
    const userLinks = document.querySelectorAll(`a[href="/${screenName}"][role="link"]`);

    userLinks.forEach((userLink) => {
        // 找到包含该用户链接的推文容器
        const tweetContainer = userLink.closest('article[role="article"]');
        if (!tweetContainer) return;

        // 查找推文正文区域
        const tweetText = tweetContainer.querySelector('[data-testid="tweetText"]');
        if (!tweetText) return;

        // 检查是否已经注入过用户信息（避免重复注入）
        const existingInfo = tweetContainer.querySelector('.x-enhancer-container');
        if (existingInfo) return;

        // 创建用户信息容器
        const infoContainer = document.createElement('div');
        infoContainer.className = 'x-enhancer-container';
        // 添加数据属性用于标识用户
        infoContainer.setAttribute('data-user', screenName);

        const createdAt = new Date(userData["创建时间"]);
        const formattedDate = `${createdAt.getFullYear()}-${String(createdAt.getMonth() + 1).padStart(2, '0')}-${String(createdAt.getDate()).padStart(2, '0')}`;
        const followers = userData["关注者"];
        const following = userData["正在关注"];
        const posts = userData["帖子数"];

        infoContainer.innerHTML = `
          <span class="x-enhancer-item">注册于: ${formattedDate}</span>
          <span class="x-enhancer-item">帖子: ${posts.toLocaleString()}</span>
          <span class="x-enhancer-item">正在关注: ${following.toLocaleString()}</span>
          <span class="x-enhancer-item">关注者: ${followers.toLocaleString()}</span>
        `;

        // 将用户信息插入到推文正文之前
        tweetText.parentNode.insertBefore(infoContainer, tweetText);
        console.log(`X User Info Enhancer: Injected info for ${screenName}`);
    });
  });
};

// 重新注入所有已知用户的信息
const reinjectAllUserInfo = () => {
    if (!isEnabled || globalUserData.size === 0) return;

    console.log('X User Info Enhancer: Re-injecting all user info...');
    injectUserInfo(globalUserData);
};

// 启动DOM观察器
const startDOMObserver = () => {
    if (mutationObserver) {
        mutationObserver.disconnect();
    }

    mutationObserver = new MutationObserver((mutations) => {
        let shouldReinject = false;

        mutations.forEach((mutation) => {
            // 检查是否有新的推文容器被添加
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // 检查是否是推文容器或包含推文容器
                        if (node.matches && (
                            node.matches('article[role="article"]') ||
                            node.querySelector('article[role="article"]')
                        )) {
                            shouldReinject = true;
                        }
                    }
                });
            }
        });

        if (shouldReinject) {
            // 使用防抖，避免频繁重新注入
            clearTimeout(window.reinjectTimeout);
            window.reinjectTimeout = setTimeout(reinjectAllUserInfo, 300);
        }
    });

    // 观察整个文档的变化
    mutationObserver.observe(document.body, {
        childList: true,
        subtree: true
    });

    console.log('X User Info Enhancer: DOM observer started.');
};

// 定期重新注入（处理虚拟滚动的备用方案）
const startPeriodicInjection = () => {
    setInterval(() => {
        if (isEnabled && globalUserData.size > 0) {
            reinjectAllUserInfo();
        }
    }, 2000); // 每2秒检查一次

    console.log('X User Info Enhancer: Periodic injection started.');
};

init();
