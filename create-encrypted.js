// 创建加密文件的脚本
const fs = require('fs');

// 简单的XOR加密函数
function encrypt(text, key) {
    let encrypted = '';
    for (let i = 0; i < text.length; i++) {
        const charCode = text.charCodeAt(i);
        const keyChar = key.charCodeAt(i % key.length);
        encrypted += String.fromCharCode(charCode ^ keyChar);
    }
    return Buffer.from(encrypted).toString('base64');
}

// 计算校验和
function calculateChecksum(data) {
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
        const char = data.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
    }
    return hash.toString(16);
}

// 读取原始文件
const originalContent = fs.readFileSync('content.js.backup', 'utf8');
const key = 'XEnhancer2024SecureKey9876543210';

// 加密内容
const encryptedData = encrypt(originalContent, key);

// 创建加密文件对象
const encryptedFile = {
    version: '1.0',
    originalName: 'content.js',
    encryptedData: encryptedData,
    timestamp: Date.now()
};

// 写入加密文件
fs.writeFileSync('content.encrypted.json', JSON.stringify(encryptedFile, null, 2));

// 验证密钥文件的校验和
const keyChecksum = calculateChecksum(key);
console.log('密钥校验和:', keyChecksum);

// 更新密钥文件
const keyFile = {
    version: '1.0',
    algorithm: 'XOR-Base64',
    key: key,
    timestamp: Date.now(),
    checksum: keyChecksum
};

fs.writeFileSync('x-enhancer-key.json', JSON.stringify(keyFile, null, 2));

console.log('✅ 加密文件创建完成!');
console.log('- content.encrypted.json: 加密的核心代码');
console.log('- x-enhancer-key.json: 密钥文件');
console.log('- 密钥校验和:', keyChecksum);
