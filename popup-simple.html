<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>X User Info Enhancer</title>
  <style>
    body { 
      font-family: Arial, sans-serif; 
      width: 300px; 
      padding: 15px; 
      margin: 0;
      background-color: #f5f5f5;
    }
    
    .header {
      text-align: center;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ddd;
    }
    
    .header h3 {
      margin: 0;
      color: #1da1f2;
      font-size: 16px;
    }
    
    .section {
      background: white;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 10px;
      border: 1px solid #ddd;
    }
    
    .auth-section {
      border-color: #ff6b6b;
      background-color: #fff5f5;
    }
    
    .auth-section.unlocked {
      border-color: #51cf66;
      background-color: #f8fff9;
    }
    
    .status {
      margin-bottom: 10px;
      font-weight: bold;
      padding: 5px;
    }
    
    .status.locked {
      color: #e03131;
    }
    
    .status.unlocked {
      color: #2b8a3e;
    }
    
    .file-input {
      width: 100%;
      padding: 5px;
      margin-bottom: 10px;
      border: 1px solid #ccc;
      border-radius: 3px;
    }
    
    .btn {
      background-color: #1da1f2;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 3px;
      cursor: pointer;
      font-size: 12px;
      width: 100%;
      margin-bottom: 5px;
    }
    
    .btn:hover {
      background-color: #1991db;
    }
    
    .btn.danger {
      background-color: #e03131;
    }
    
    .btn.danger:hover {
      background-color: #c92a2a;
    }
    
    .toggle-container { 
      display: flex; 
      align-items: center; 
      justify-content: space-between; 
    }
    
    .switch {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 24px;
    }
    
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px;
    }
    
    .slider:before {
      position: absolute;
      content: "";
      height: 18px;
      width: 18px;
      left: 3px;
      bottom: 3px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    
    input:checked + .slider {
      background-color: #1da1f2;
    }
    
    input:checked + .slider:before {
      transform: translateX(26px);
    }
    
    .message {
      font-size: 11px;
      margin-top: 8px;
      padding: 6px;
      border-radius: 3px;
      display: none;
    }
    
    .message.error {
      background-color: #ffe0e0;
      color: #c92a2a;
      border: 1px solid #ffc9c9;
    }
    
    .message.success {
      background-color: #e6ffed;
      color: #2b8a3e;
      border: 1px solid #c3fae8;
    }
    
    .key-info {
      font-size: 10px;
      color: #666;
      margin-top: 5px;
      display: none;
    }
  </style>
</head>
<body>
  <div class="header">
    <h3>X User Info Enhancer</h3>
  </div>

  <div class="section auth-section" id="authSection">
    <div class="status locked" id="authStatus">
      <span id="statusText">需要密钥文件授权</span>
    </div>
    
    <div id="keyInputContainer">
      <input type="file" id="keyFileInput" class="file-input" accept=".json" />
      <button id="loadKeyBtn" class="btn">加载密钥文件</button>
      <button id="clearKeyBtn" class="btn danger" style="display: none;">清除密钥</button>
    </div>
    
    <div id="keyInfo" class="key-info"></div>
    <div id="message" class="message"></div>
  </div>

  <div class="section">
    <div class="toggle-container">
      <span>启用增强功能</span>
      <label class="switch">
        <input type="checkbox" id="enabledToggle">
        <span class="slider"></span>
      </label>
    </div>
  </div>

  <script src="popup-simple.js"></script>
</body>
</html>
