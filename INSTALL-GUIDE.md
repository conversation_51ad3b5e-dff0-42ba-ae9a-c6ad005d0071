# 🔐 X User Info Enhancer 加密版 - 安装指南

## 📦 快速安装

### 1. 下载插件
确保您有以下文件：
- `manifest.json`
- `content.js` (加密保护版)
- `content.encrypted.json` (加密的核心代码)
- `x-enhancer-key.json` (密钥文件) ⚠️ **必需**
- `popup.html` / `popup.js`
- `interceptor.js`
- `styles.css`
- `icons/` 文件夹

### 2. 安装到Chrome
1. 打开Chrome浏览器
2. 地址栏输入：`chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹

### 3. 加载密钥文件 🔑
1. 点击浏览器工具栏中的插件图标
2. 在弹出界面中点击"选择文件"
3. 选择 `x-enhancer-key.json` 文件
4. 点击"加载密钥文件"
5. 看到"🔓 已授权"状态

### 4. 开始使用
- 访问 [X.com](https://x.com) 或 [Twitter.com](https://twitter.com)
- 插件会自动在推文中显示用户详细信息
- 包括：注册时间、帖子数、关注数、粉丝数

## ⚠️ 重要提醒

### 密钥文件安全
- **妥善保管** `x-enhancer-key.json` 文件
- **不要分享**给他人
- **建议备份**到安全位置
- 丢失后插件将无法使用

### 故障排除
| 问题 | 解决方案 |
|------|----------|
| 插件无法启动 | 检查是否已加载密钥文件 |
| 显示"需要密钥文件授权" | 重新加载密钥文件 |
| 功能不正常 | 刷新页面，检查插件是否启用 |

## 🔧 高级功能

### 重新生成密钥
如需重新生成密钥文件：
1. 打开 `key-generator.html`
2. 点击"生成密钥文件"
3. 下载新的密钥文件
4. 重新加密核心代码

### 测试系统
打开 `test-encryption.html` 验证加密系统是否正常工作

## 📞 技术支持

遇到问题？
1. 查看浏览器控制台错误信息
2. 确认所有文件完整
3. 验证密钥文件格式正确

---

**版本**: v1.0 加密保护版  
**更新时间**: 2024年9月  
**兼容性**: Chrome 88+
