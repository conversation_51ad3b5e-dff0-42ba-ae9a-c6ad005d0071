// 简化的加密设置脚本 - 使用浏览器环境
class SimpleEncryption {
    constructor() {
        this.key = this.generateSimpleKey();
    }

    // 生成简单密钥
    generateSimpleKey() {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let key = '';
        for (let i = 0; i < 32; i++) {
            key += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return key;
    }

    // 简单的XOR加密
    encrypt(text, key) {
        let encrypted = '';
        for (let i = 0; i < text.length; i++) {
            const charCode = text.charCodeAt(i);
            const keyChar = key.charCodeAt(i % key.length);
            encrypted += String.fromCharCode(charCode ^ keyChar);
        }
        return btoa(encrypted); // Base64编码
    }

    // 简单的XOR解密
    decrypt(encryptedText, key) {
        const encrypted = atob(encryptedText); // Base64解码
        let decrypted = '';
        for (let i = 0; i < encrypted.length; i++) {
            const charCode = encrypted.charCodeAt(i);
            const keyChar = key.charCodeAt(i % key.length);
            decrypted += String.fromCharCode(charCode ^ keyChar);
        }
        return decrypted;
    }

    // 生成密钥文件内容
    generateKeyFile() {
        const keyFile = {
            version: '1.0',
            algorithm: 'XOR-Base64',
            key: this.key,
            timestamp: Date.now(),
            checksum: this.calculateChecksum(this.key)
        };
        return JSON.stringify(keyFile, null, 2);
    }

    // 计算简单校验和
    calculateChecksum(data) {
        let hash = 0;
        for (let i = 0; i < data.length; i++) {
            const char = data.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash.toString(16);
    }

    // 验证密钥文件
    validateKeyFile(keyFileContent) {
        try {
            const keyFile = JSON.parse(keyFileContent);
            
            if (!keyFile.version || !keyFile.algorithm || !keyFile.key || !keyFile.checksum) {
                throw new Error('密钥文件格式无效');
            }

            const calculatedChecksum = this.calculateChecksum(keyFile.key);
            if (calculatedChecksum !== keyFile.checksum) {
                throw new Error('密钥文件已损坏或被篡改');
            }

            return keyFile;
        } catch (error) {
            throw new Error(`密钥文件验证失败: ${error.message}`);
        }
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SimpleEncryption;
} else {
    window.SimpleEncryption = SimpleEncryption;
}
